export type VariantsType = {
	durationIn?: number;
	durationOut?: number;
	easeIn?: [];
	easeOut?: [];
	distance?: number;
};

export type TranHoverType = {
	duration?: number;
	ease?: [];
};
export type TranEnterType = {
	durationIn?: number;
	easeIn?: [];
};
export type TranExitType = {
	durationOut?: number;
	easeOut?: [];
};

export type BackgroundType = {
	duration?: number;
	ease?: [];
	colors?: string[];
};
