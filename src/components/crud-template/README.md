# CRUD Template Component

A comprehensive, reusable CRUD (Create, Read, Update, Delete) template component for the slash-admin project. This template provides a complete data management solution with advanced features like filtering, searching, sorting, pagination, bulk operations, and data export.

## Features

### ✅ Core CRUD Operations
- **Create**: Modal-based form with validation
- **Read**: Data table with sorting and pagination
- **Update**: Edit existing records with pre-filled forms
- **Delete**: Single and bulk delete operations

### ✅ Advanced Data Management
- **Search**: Global search across multiple columns
- **Filter**: Column-specific filters (text, select, date, number, boolean)
- **Sort**: Multi-column sorting support
- **Pagination**: Configurable pagination with size options

### ✅ Bulk Operations
- **Bulk Delete**: Delete multiple records at once
- **Bulk Update**: Update multiple records simultaneously
- **Custom Operations**: Define your own bulk operations

### ✅ Data Export
- **CSV Export**: Export data to CSV format
- **Excel Export**: Export data to Excel format
- **Custom Fields**: Select which columns to export
- **Filtered Export**: Export only filtered/selected data

### ✅ UI/UX Features
- **Responsive Design**: Works on all screen sizes
- **Loading States**: Proper loading indicators
- **Error Handling**: User-friendly error messages
- **Toast Notifications**: Success/error feedback
- **Column Toggle**: Show/hide table columns
- **Row Selection**: Multi-select with checkboxes

## Quick Start

### 1. Define Your Entity

```typescript
import type { BaseEntity } from "@/components/crud-template";

interface Product extends BaseEntity {
  id: string;
  name: string;
  price: number;
  category: string;
  status: "active" | "inactive";
  createdAt: string;
  updatedAt: string;
}
```

### 2. Create Column Definitions

```typescript
import type { CrudColumn } from "@/components/crud-template";

const columns: CrudColumn<Product>[] = [
  {
    key: "name",
    title: "Product Name",
    sortable: true,
    filterable: true,
    searchable: true,
    filterType: "text",
  },
  {
    key: "price",
    title: "Price",
    sortable: true,
    filterable: true,
    filterType: "number",
    render: (value) => `$${value.toFixed(2)}`,
  },
  {
    key: "status",
    title: "Status",
    filterable: true,
    filterType: "select",
    filterOptions: [
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
    ],
  },
];
```

### 3. Define Form Fields

```typescript
import type { CrudFormField } from "@/components/crud-template";
import { z } from "zod";

const formFields: CrudFormField<Product>[] = [
  {
    key: "name",
    label: "Product Name",
    type: "text",
    required: true,
    placeholder: "Enter product name",
  },
  {
    key: "price",
    label: "Price",
    type: "number",
    required: true,
    placeholder: "0.00",
  },
  {
    key: "status",
    label: "Status",
    type: "select",
    required: true,
    options: [
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
    ],
  },
];

const schema = z.object({
  name: z.string().min(1, "Name is required"),
  price: z.number().min(0, "Price must be positive"),
  status: z.enum(["active", "inactive"]),
});
```

### 4. Implement Handlers

```typescript
import type { CrudHandlers } from "@/components/crud-template";

const handlers: CrudHandlers<Product> = {
  onFetch: async (params) => {
    const response = await fetch(`/api/products?${new URLSearchParams(params)}`);
    return response.json();
  },
  
  onCreate: async (data) => {
    const response = await fetch("/api/products", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
    });
    return response.json();
  },
  
  onUpdate: async (id, data) => {
    const response = await fetch(`/api/products/${id}`, {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
    });
    return response.json();
  },
  
  onDelete: async (id) => {
    await fetch(`/api/products/${id}`, { method: "DELETE" });
  },
};
```

### 5. Configure and Use the Template

```typescript
import { CrudTemplate } from "@/components/crud-template";
import type { CrudConfig } from "@/components/crud-template";

const config: CrudConfig<Product> = {
  entityName: "Product",
  entityNamePlural: "Products",
  columns,
  formFields,
  formSchema: schema,
  handlers,
  
  features: {
    create: true,
    read: true,
    update: true,
    delete: true,
    bulkDelete: true,
    export: true,
    search: true,
    filter: true,
    sort: true,
    pagination: true,
  },
  
  ui: {
    tableSize: "small",
    formLayout: "vertical",
    formModalSize: "lg",
    showRowNumbers: true,
    showRefreshButton: true,
  },
};

export function ProductManagement() {
  return (
    <CrudTemplate
      config={config}
      queryKey={["products"]}
    />
  );
}
```

## Configuration Options

### Entity Configuration
- `entityName`: Singular name (e.g., "Product")
- `entityNamePlural`: Plural name (e.g., "Products")
- `rowKey`: Unique identifier field (default: "id")

### Features
Enable/disable specific features:
```typescript
features: {
  create: boolean;
  read: boolean;
  update: boolean;
  delete: boolean;
  bulkDelete: boolean;
  bulkUpdate: boolean;
  export: boolean;
  search: boolean;
  filter: boolean;
  sort: boolean;
  pagination: boolean;
}
```

### UI Configuration
```typescript
ui: {
  tableSize: "small" | "middle" | "large";
  formLayout: "horizontal" | "vertical";
  formModalSize: "sm" | "md" | "lg" | "xl";
  showRowNumbers: boolean;
  showRefreshButton: boolean;
  showColumnToggle: boolean;
  compactMode: boolean;
}
```

### Pagination
```typescript
pagination: {
  pageSize: number;
  showSizeChanger: boolean;
  showQuickJumper: boolean;
  showTotal: boolean;
  pageSizeOptions: number[];
}
```

## Column Types

### Basic Column
```typescript
{
  key: "name",
  title: "Name",
  dataIndex: "name", // optional, defaults to key
  width: 200,
  align: "left" | "center" | "right",
}
```

### Sortable Column
```typescript
{
  key: "price",
  title: "Price",
  sortable: true,
}
```

### Filterable Column
```typescript
{
  key: "category",
  title: "Category",
  filterable: true,
  filterType: "select",
  filterOptions: [
    { label: "Electronics", value: "electronics" },
    { label: "Clothing", value: "clothing" },
  ],
}
```

### Custom Render
```typescript
{
  key: "status",
  title: "Status",
  render: (value, record, index) => (
    <Badge variant={value === "active" ? "default" : "secondary"}>
      {value}
    </Badge>
  ),
}
```

### Responsive Column
```typescript
{
  key: "description",
  title: "Description",
  responsive: "md", // Hide on screens smaller than md
}
```

## Form Field Types

### Text Input
```typescript
{
  key: "name",
  label: "Name",
  type: "text",
  placeholder: "Enter name",
  required: true,
}
```

### Select Dropdown
```typescript
{
  key: "category",
  label: "Category",
  type: "select",
  options: [
    { label: "Option 1", value: "opt1" },
    { label: "Option 2", value: "opt2" },
  ],
}
```

### Textarea
```typescript
{
  key: "description",
  label: "Description",
  type: "textarea",
  placeholder: "Enter description",
}
```

### Number Input
```typescript
{
  key: "price",
  label: "Price",
  type: "number",
  placeholder: "0.00",
}
```

### Date Picker
```typescript
{
  key: "startDate",
  label: "Start Date",
  type: "date",
}
```

### Checkbox
```typescript
{
  key: "isActive",
  label: "Active",
  type: "checkbox",
  placeholder: "Mark as active",
}
```

### Switch
```typescript
{
  key: "enabled",
  label: "Enabled",
  type: "switch",
  placeholder: "Enable feature",
}
```

### Custom Field
```typescript
{
  key: "custom",
  label: "Custom Field",
  type: "custom",
  render: (field, form) => (
    <CustomComponent {...field} form={form} />
  ),
}
```

## Filter Types

### Text Filter
```typescript
filterType: "text" // Contains search
```

### Select Filter
```typescript
filterType: "select"
filterOptions: [
  { label: "Option 1", value: "value1" },
  { label: "Option 2", value: "value2" },
]
```

### Number Filter
```typescript
filterType: "number" // Supports operators: =, ≠, >, ≥, <, ≤
```

### Date Filter
```typescript
filterType: "date" // Single date picker
```

### Date Range Filter
```typescript
filterType: "dateRange" // From/To date pickers
```

### Boolean Filter
```typescript
filterType: "boolean" // Yes/No/All dropdown
```

## Bulk Operations

### Default Operations
The template provides default bulk delete and update operations:

```typescript
// Automatically created if handlers are provided
handlers: {
  onBulkDelete: async (ids: string[]) => {
    // Delete multiple records
  },
  onBulkUpdate: async (ids: string[], data: Partial<T>) => {
    // Update multiple records
  },
}
```

### Custom Bulk Operations
```typescript
bulkOperations: [
  {
    key: "archive",
    label: "Archive Selected",
    icon: "mdi:archive",
    variant: "outline",
    action: async (selectedIds, selectedRecords) => {
      // Custom bulk operation logic
    },
    requiresConfirmation: true,
    confirmMessage: "Archive selected items?",
  },
]
```

## Data Export

### Basic Export
```typescript
exportConfig: {
  formats: ["csv", "excel"],
  includeHeaders: true,
}
```

### Custom Export Handler
```typescript
handlers: {
  onExport: async (params) => {
    // Custom export logic
    const { filters, sort, search, selectedIds, format } = params;
    // Generate and download file
  },
}
```

## Integration with React Query

The template uses React Query for data fetching and caching:

```typescript
// Query key should be unique for each entity
<CrudTemplate
  config={config}
  queryKey={["products"]} // Will be extended with filters, pagination, etc.
/>
```

## Error Handling

The template handles errors gracefully:
- Network errors are displayed as toast notifications
- Form validation errors are shown inline
- Loading states prevent multiple submissions

## Styling and Theming

The template uses the project's existing design system:
- shadcn/ui components for consistent styling
- Tailwind CSS for responsive design
- Ant Design Table for advanced table features
- Supports light/dark themes automatically

## Performance Considerations

- **Virtualization**: Large datasets are handled efficiently
- **Debounced Search**: Search input is debounced to reduce API calls
- **Optimistic Updates**: UI updates immediately for better UX
- **Query Caching**: React Query caches data to reduce network requests

## Accessibility

The template follows accessibility best practices:
- Keyboard navigation support
- Screen reader friendly
- ARIA labels and descriptions
- Focus management in modals

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Examples

See the `examples/` directory for complete implementations:
- `product-crud.tsx`: Full product management example
- More examples coming soon...

## Contributing

When extending the template:
1. Follow existing patterns and conventions
2. Add proper TypeScript types
3. Include tests for new features
4. Update documentation
5. Ensure accessibility compliance
