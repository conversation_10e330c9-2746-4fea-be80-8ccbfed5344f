import type { ReactNode } from "react";
import type { z } from "zod";
import type { UseFormReturn } from "react-hook-form";

// Base entity interface that all CRUD entities should extend
export interface BaseEntity {
  id: string;
  createdAt?: string;
  updatedAt?: string;
}

// Column definition for the data table
export interface CrudColumn<T = any> {
  key: keyof T | string;
  title: string;
  dataIndex?: keyof T | string;
  width?: number | string;
  align?: "left" | "center" | "right";
  sortable?: boolean;
  filterable?: boolean;
  searchable?: boolean;
  render?: (value: any, record: T, index: number) => ReactNode;
  filterType?: "text" | "select" | "date" | "dateRange" | "number" | "boolean";
  filterOptions?: Array<{ label: string; value: any }>;
  hidden?: boolean;
  responsive?: "xs" | "sm" | "md" | "lg" | "xl";
}

// Form field definition for create/edit forms
export interface CrudFormField<T = any> {
  key: keyof T | string;
  label: string;
  type: "text" | "email" | "password" | "number" | "textarea" | "select" | "multiselect" | "checkbox" | "switch" | "date" | "datetime" | "file" | "custom";
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  hidden?: boolean;
  options?: Array<{ label: string; value: any }>;
  validation?: z.ZodSchema<any>;
  render?: (field: any, form: UseFormReturn<any>) => ReactNode;
  gridCols?: 1 | 2 | 3 | 4 | 6 | 12;
  description?: string;
  defaultValue?: any;
}

// Filter types
export interface FilterValue {
  type: "text" | "select" | "date" | "dateRange" | "number" | "boolean";
  value: any;
  operator?: "eq" | "ne" | "gt" | "gte" | "lt" | "lte" | "contains" | "startsWith" | "endsWith" | "in" | "between";
}

export interface CrudFilters {
  [key: string]: FilterValue;
}

// Pagination configuration
export interface PaginationConfig {
  page: number;
  pageSize: number;
  total: number;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: boolean;
  pageSizeOptions?: number[];
}

// Sort configuration
export interface SortConfig {
  field: string;
  direction: "asc" | "desc";
}

// Bulk operation types
export interface BulkOperation<T = any> {
  key: string;
  label: string;
  icon?: string;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  action: (selectedIds: string[], selectedRecords: T[]) => Promise<void> | void;
  confirmMessage?: string;
  requiresConfirmation?: boolean;
}

// Export configuration
export interface ExportConfig {
  filename?: string;
  formats: Array<"csv" | "excel">;
  includeHeaders?: boolean;
  selectedOnly?: boolean;
  customFields?: Array<{
    key: string;
    label: string;
    transform?: (value: any, record: any) => any;
  }>;
}

// CRUD operation handlers
export interface CrudHandlers<T extends BaseEntity> {
  onFetch: (params: {
    page: number;
    pageSize: number;
    filters: CrudFilters;
    sort?: SortConfig;
    search?: string;
  }) => Promise<{ data: T[]; total: number }>;
  onCreate: (data: Omit<T, "id" | "createdAt" | "updatedAt">) => Promise<T>;
  onUpdate: (id: string, data: Partial<T>) => Promise<T>;
  onDelete: (id: string) => Promise<void>;
  onBulkDelete?: (ids: string[]) => Promise<void>;
  onBulkUpdate?: (ids: string[], data: Partial<T>) => Promise<void>;
  onExport?: (params: {
    filters: CrudFilters;
    sort?: SortConfig;
    search?: string;
    selectedIds?: string[];
    format: "csv" | "excel";
  }) => Promise<void>;
}

// Main CRUD configuration
export interface CrudConfig<T extends BaseEntity> {
  // Entity configuration
  entityName: string;
  entityNamePlural: string;
  
  // Table configuration
  columns: CrudColumn<T>[];
  rowKey?: keyof T | string;
  
  // Form configuration
  formFields: CrudFormField<T>[];
  formSchema: z.ZodSchema<any>;
  
  // Feature flags
  features: {
    create?: boolean;
    read?: boolean;
    update?: boolean;
    delete?: boolean;
    bulkDelete?: boolean;
    bulkUpdate?: boolean;
    export?: boolean;
    search?: boolean;
    filter?: boolean;
    sort?: boolean;
    pagination?: boolean;
  };
  
  // UI configuration
  ui: {
    tableSize?: "small" | "middle" | "large";
    formLayout?: "horizontal" | "vertical";
    formModalSize?: "sm" | "md" | "lg" | "xl";
    showRowNumbers?: boolean;
    showRefreshButton?: boolean;
    showColumnToggle?: boolean;
    compactMode?: boolean;
  };
  
  // Pagination configuration
  pagination?: Partial<PaginationConfig>;
  
  // Bulk operations
  bulkOperations?: BulkOperation<T>[];
  
  // Export configuration
  exportConfig?: ExportConfig;
  
  // Handlers
  handlers: CrudHandlers<T>;
  
  // Custom components
  customComponents?: {
    tableHeader?: ReactNode;
    tableFooter?: ReactNode;
    formHeader?: ReactNode;
    formFooter?: ReactNode;
    emptyState?: ReactNode;
    loadingState?: ReactNode;
    errorState?: ReactNode;
  };
}

// Component state interfaces
export interface CrudState<T extends BaseEntity> {
  data: T[];
  loading: boolean;
  error: string | null;
  selectedRowKeys: string[];
  selectedRows: T[];
  filters: CrudFilters;
  sort?: SortConfig;
  search: string;
  pagination: PaginationConfig;
  formModalOpen: boolean;
  formMode: "create" | "edit";
  editingRecord?: T;
  bulkOperationLoading: boolean;
  exportLoading: boolean;
}

// Action types for state management
export type CrudAction<T extends BaseEntity> =
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_DATA"; payload: { data: T[]; total: number } }
  | { type: "SET_ERROR"; payload: string | null }
  | { type: "SET_SELECTED_ROWS"; payload: { keys: string[]; rows: T[] } }
  | { type: "SET_FILTERS"; payload: CrudFilters }
  | { type: "SET_SORT"; payload: SortConfig | undefined }
  | { type: "SET_SEARCH"; payload: string }
  | { type: "SET_PAGINATION"; payload: Partial<PaginationConfig> }
  | { type: "SET_FORM_MODAL"; payload: { open: boolean; mode: "create" | "edit"; record?: T } }
  | { type: "SET_BULK_OPERATION_LOADING"; payload: boolean }
  | { type: "SET_EXPORT_LOADING"; payload: boolean };
