import { Icon } from "@/components/icon";
import { But<PERSON> } from "@/ui/button";
import { Calendar } from "@/ui/calendar";
import { Checkbox } from "@/ui/checkbox";
import { Di<PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/ui/dialog";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/ui/dropdown-menu";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/ui/form";
import { Input } from "@/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/ui/select";
import { Switch } from "@/ui/switch";
import { Textarea } from "@/ui/textarea";
import { cn } from "@/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import type { BaseEntity, CrudFormField } from "./types";

interface FormModalProps<T extends BaseEntity> {
	open: boolean;
	mode: "create" | "edit";
	title?: string;
	fields: CrudFormField<T>[];
	schema: z.ZodSchema<any>;
	initialData?: Partial<T>;
	loading?: boolean;
	onSubmit: (data: any) => Promise<void> | void;
	onCancel: () => void;
	size?: "sm" | "md" | "lg" | "xl";
	layout?: "horizontal" | "vertical";
	customHeader?: React.ReactNode;
	customFooter?: React.ReactNode;
}

export function FormModal<T extends BaseEntity>({
	open,
	mode,
	title,
	fields,
	schema,
	initialData,
	loading = false,
	onSubmit,
	onCancel,
	size = "md",
	layout = "vertical",
	customHeader,
	customFooter,
}: FormModalProps<T>) {
	const form = useForm({
		resolver: zodResolver(schema),
		defaultValues: initialData || {},
	});

	// Reset form when modal opens/closes or initial data changes
	useEffect(() => {
		if (open) {
			form.reset(initialData || {});
		}
	}, [open, initialData, form]);

	const handleSubmit = async (data: any) => {
		try {
			await onSubmit(data);
			form.reset();
		} catch (error) {
			console.error("Form submission error:", error);
		}
	};

	const renderField = (field: CrudFormField<T>) => {
		if (field.hidden) return null;

		return (
			<FormField
				key={String(field.key)}
				control={form.control}
				name={String(field.key)}
				render={({ field: formField }) => (
					<FormItem
						className={cn(
							layout === "horizontal" && "grid grid-cols-4 items-center gap-4",
							field.gridCols && `col-span-${field.gridCols}`,
						)}
					>
						<FormLabel className={cn(layout === "horizontal" && "text-right")}>
							{field.label}
							{field.required && <span className="text-destructive ml-1">*</span>}
						</FormLabel>
						<div className={cn(layout === "horizontal" && "col-span-3")}>
							<FormControl>{renderFieldInput(field, formField)}</FormControl>
							{field.description && <FormDescription>{field.description}</FormDescription>}
							<FormMessage />
						</div>
					</FormItem>
				)}
			/>
		);
	};

	const renderFieldInput = (field: CrudFormField<T>, formField: any) => {
		if (field.render) {
			return field.render(formField, form);
		}

		switch (field.type) {
			case "text":
			case "email":
			case "password":
			case "number":
				return (
					<Input
						{...formField}
						type={field.type}
						placeholder={field.placeholder}
						disabled={field.disabled || loading}
					/>
				);

			case "textarea":
				return (
					<Textarea {...formField} placeholder={field.placeholder} disabled={field.disabled || loading} rows={4} />
				);

			case "select":
				return (
					<Select value={formField.value} onValueChange={formField.onChange} disabled={field.disabled || loading}>
						<SelectTrigger>
							<SelectValue placeholder={field.placeholder} />
						</SelectTrigger>
						<SelectContent>
							{field.options?.map((option) => (
								<SelectItem key={option.value} value={option.value}>
									{option.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				);

			case "checkbox":
				return (
					<div className="flex items-center space-x-2">
						<Checkbox
							checked={formField.value}
							onCheckedChange={formField.onChange}
							disabled={field.disabled || loading}
						/>
						<span className="text-sm">{field.placeholder}</span>
					</div>
				);

			case "switch":
				return (
					<div className="flex items-center space-x-2">
						<Switch
							checked={formField.value}
							onCheckedChange={formField.onChange}
							disabled={field.disabled || loading}
						/>
						<span className="text-sm">{field.placeholder}</span>
					</div>
				);

			case "date":
				return (
					<Popover>
						<PopoverTrigger asChild>
							<Button
								variant="outline"
								className={cn(
									"w-full justify-start text-left font-normal",
									!formField.value && "text-muted-foreground",
								)}
								disabled={field.disabled || loading}
							>
								<Icon icon="mdi:calendar" className="mr-2 h-4 w-4" />
								{formField.value ? (
									format(new Date(formField.value), "PPP")
								) : (
									<span>{field.placeholder || "Pick a date"}</span>
								)}
							</Button>
						</PopoverTrigger>
						<PopoverContent className="w-auto p-0" align="start">
							<Calendar
								mode="single"
								selected={formField.value ? new Date(formField.value) : undefined}
								onSelect={(date) => formField.onChange(date?.toISOString())}
								initialFocus
							/>
						</PopoverContent>
					</Popover>
				);

			case "multiselect":
				// Implementation for multi-select would go here
				return <div className="text-sm text-muted-foreground">Multi-select not implemented yet</div>;

			case "file":
				return (
					<Input
						type="file"
						onChange={(e) => {
							const file = e.target.files?.[0];
							formField.onChange(file);
						}}
						disabled={field.disabled || loading}
					/>
				);

			default:
				return <Input {...formField} placeholder={field.placeholder} disabled={field.disabled || loading} />;
		}
	};

	const modalSizeClasses = {
		sm: "sm:max-w-md",
		md: "sm:max-w-lg",
		lg: "sm:max-w-2xl",
		xl: "sm:max-w-4xl",
	};

	const defaultTitle = mode === "create" ? "Create New Record" : "Edit Record";

	return (
		<Dialog open={open} onOpenChange={(open) => !open && onCancel()}>
			<DialogContent className={cn("max-h-[90vh] overflow-y-auto", modalSizeClasses[size])}>
				<DialogHeader>{customHeader || <DialogTitle>{title || defaultTitle}</DialogTitle>}</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
						<div
							className={cn(
								"grid gap-4",
								layout === "vertical" && "grid-cols-1",
								layout === "horizontal" && "grid-cols-1",
							)}
						>
							{fields.filter((field) => !field.hidden).map((field) => renderField(field))}
						</div>

						<DialogFooter>
							{customFooter || (
								<>
									<Button type="button" variant="outline" onClick={onCancel} disabled={loading}>
										Cancel
									</Button>
									<Button type="submit" disabled={loading}>
										{loading && <Icon icon="mdi:loading" className="mr-2 h-4 w-4 animate-spin" />}
										{mode === "create" ? "Create" : "Update"}
									</Button>
								</>
							)}
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
