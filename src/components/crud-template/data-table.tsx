import { useState, useMemo } from "react";
import { Table } from "antd";
import type { ColumnsType } from "antd/es/table";
import { Icon } from "@/components/icon";
import { Button } from "@/ui/button";
import { Checkbox } from "@/ui/checkbox";
import { Badge } from "@/ui/badge";
import { Skeleton } from "@/ui/skeleton";
import { cn } from "@/utils";
import type { BaseEntity, CrudColumn, CrudState, SortConfig } from "./types";

interface DataTableProps<T extends BaseEntity> {
  data: T[];
  columns: CrudColumn<T>[];
  loading: boolean;
  selectedRowKeys: string[];
  onSelectionChange: (keys: string[], rows: T[]) => void;
  onSort: (sort: SortConfig | undefined) => void;
  onEdit?: (record: T) => void;
  onDelete?: (record: T) => void;
  onView?: (record: T) => void;
  currentSort?: SortConfig;
  rowKey?: keyof T | string;
  size?: "small" | "middle" | "large";
  showRowNumbers?: boolean;
  emptyState?: React.ReactNode;
  className?: string;
}

export function DataTable<T extends BaseEntity>({
  data,
  columns,
  loading,
  selectedRowKeys,
  onSelectionChange,
  onSort,
  onEdit,
  onDelete,
  onView,
  currentSort,
  rowKey = "id",
  size = "small",
  showRowNumbers = false,
  emptyState,
  className,
}: DataTableProps<T>) {
  const [hiddenColumns, setHiddenColumns] = useState<Set<string>>(new Set());

  // Convert CRUD columns to Ant Design table columns
  const tableColumns = useMemo(() => {
    const cols: ColumnsType<T> = [];

    // Row numbers column
    if (showRowNumbers) {
      cols.push({
        title: "#",
        key: "rowNumber",
        width: 60,
        align: "center",
        render: (_, __, index) => index + 1,
      });
    }

    // Data columns
    columns
      .filter(col => !hiddenColumns.has(String(col.key)))
      .forEach(col => {
        const antdCol: any = {
          title: col.title,
          dataIndex: col.dataIndex || col.key,
          key: String(col.key),
          width: col.width,
          align: col.align || "left",
          render: col.render,
          sorter: col.sortable,
          sortOrder: currentSort?.field === col.key 
            ? (currentSort.direction === "asc" ? "ascend" : "descend")
            : null,
          onHeaderCell: () => ({
            className: col.sortable ? "cursor-pointer" : "",
          }),
        };

        // Handle responsive columns
        if (col.responsive) {
          antdCol.responsive = [col.responsive];
        }

        cols.push(antdCol);
      });

    // Actions column
    if (onEdit || onDelete || onView) {
      cols.push({
        title: "Actions",
        key: "actions",
        align: "center",
        width: 120,
        render: (_, record) => (
          <div className="flex items-center justify-center gap-1">
            {onView && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onView(record)}
                className="h-8 w-8"
              >
                <Icon icon="mdi:eye" size={16} />
              </Button>
            )}
            {onEdit && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onEdit(record)}
                className="h-8 w-8"
              >
                <Icon icon="solar:pen-bold-duotone" size={16} />
              </Button>
            )}
            {onDelete && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onDelete(record)}
                className="h-8 w-8 text-destructive hover:text-destructive"
              >
                <Icon icon="mingcute:delete-2-fill" size={16} />
              </Button>
            )}
          </div>
        ),
      });
    }

    return cols;
  }, [columns, hiddenColumns, currentSort, onEdit, onDelete, onView, showRowNumbers]);

  // Row selection configuration
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[], rows: T[]) => {
      onSelectionChange(keys as string[], rows);
    },
    getCheckboxProps: (record: T) => ({
      name: String(record[rowKey as keyof T]),
    }),
  };

  // Handle table change (sorting, pagination, etc.)
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    if (sorter && sorter.field) {
      const sortConfig: SortConfig = {
        field: sorter.field,
        direction: sorter.order === "ascend" ? "asc" : "desc",
      };
      onSort(sorter.order ? sortConfig : undefined);
    } else {
      onSort(undefined);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className={cn("space-y-4", className)}>
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="flex items-center space-x-4">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 flex-1" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-16" />
          </div>
        ))}
      </div>
    );
  }

  // Empty state
  if (!data.length && !loading) {
    return (
      <div className={cn("flex flex-col items-center justify-center py-12", className)}>
        {emptyState || (
          <>
            <Icon icon="mdi:database-off" size={48} className="text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No data found</h3>
            <p className="text-muted-foreground text-center max-w-md">
              There are no records to display. Try adjusting your filters or create a new record.
            </p>
          </>
        )}
      </div>
    );
  }

  return (
    <div className={cn("w-full", className)}>
      <Table
        rowKey={String(rowKey)}
        columns={tableColumns}
        dataSource={data}
        rowSelection={rowSelection}
        size={size}
        scroll={{ x: "max-content" }}
        pagination={false}
        onChange={handleTableChange}
        className="crud-data-table"
      />
    </div>
  );
}

// Column visibility toggle component
export function ColumnToggle<T extends BaseEntity>({
  columns,
  hiddenColumns,
  onToggle,
}: {
  columns: CrudColumn<T>[];
  hiddenColumns: Set<string>;
  onToggle: (columnKey: string) => void;
}) {
  return (
    <div className="space-y-2">
      <h4 className="font-medium text-sm">Toggle Columns</h4>
      <div className="space-y-2 max-h-60 overflow-y-auto">
        {columns.map(col => (
          <div key={String(col.key)} className="flex items-center space-x-2">
            <Checkbox
              id={String(col.key)}
              checked={!hiddenColumns.has(String(col.key))}
              onCheckedChange={() => onToggle(String(col.key))}
            />
            <label
              htmlFor={String(col.key)}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {col.title}
            </label>
          </div>
        ))}
      </div>
    </div>
  );
}
