import { useReducer, useEffect, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader } from "@/ui/card";
import { Button } from "@/ui/button";
import { Separator } from "@/ui/separator";
import { Icon } from "@/components/icon";
import { toast } from "sonner";
import { DataTable } from "./data-table";
import { FormModal } from "./form-modal";
import { FilterPanel } from "./filter-panel";
import { BulkOperations, createDefaultBulkOperations } from "./bulk-operations";
import { ExportButton, exportUtils } from "./export-utils";
import { cn } from "@/utils";
import type { BaseEntity, CrudConfig, CrudState, CrudAction, PaginationConfig } from "./types";

// CRUD state reducer
function crudReducer<T extends BaseEntity>(
  state: CrudState<T>,
  action: CrudAction<T>
): CrudState<T> {
  switch (action.type) {
    case "SET_LOADING":
      return { ...state, loading: action.payload };
    case "SET_DATA":
      return { 
        ...state, 
        data: action.payload.data, 
        pagination: { ...state.pagination, total: action.payload.total },
        loading: false,
        error: null 
      };
    case "SET_ERROR":
      return { ...state, error: action.payload, loading: false };
    case "SET_SELECTED_ROWS":
      return { 
        ...state, 
        selectedRowKeys: action.payload.keys, 
        selectedRows: action.payload.rows 
      };
    case "SET_FILTERS":
      return { ...state, filters: action.payload };
    case "SET_SORT":
      return { ...state, sort: action.payload };
    case "SET_SEARCH":
      return { ...state, search: action.payload };
    case "SET_PAGINATION":
      return { 
        ...state, 
        pagination: { ...state.pagination, ...action.payload } 
      };
    case "SET_FORM_MODAL":
      return { 
        ...state, 
        formModalOpen: action.payload.open,
        formMode: action.payload.mode,
        editingRecord: action.payload.record 
      };
    case "SET_BULK_OPERATION_LOADING":
      return { ...state, bulkOperationLoading: action.payload };
    case "SET_EXPORT_LOADING":
      return { ...state, exportLoading: action.payload };
    default:
      return state;
  }
}

interface CrudTemplateProps<T extends BaseEntity> {
  config: CrudConfig<T>;
  queryKey: string[];
  className?: string;
}

export function CrudTemplate<T extends BaseEntity>({
  config,
  queryKey,
  className,
}: CrudTemplateProps<T>) {
  const queryClient = useQueryClient();

  // Initialize state
  const initialState: CrudState<T> = {
    data: [],
    loading: false,
    error: null,
    selectedRowKeys: [],
    selectedRows: [],
    filters: {},
    search: "",
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: true,
      pageSizeOptions: [10, 20, 50, 100],
      ...config.pagination,
    },
    formModalOpen: false,
    formMode: "create",
    bulkOperationLoading: false,
    exportLoading: false,
  };

  const [state, dispatch] = useReducer(crudReducer<T>, initialState);

  // Data fetching query
  const { data: queryData, isLoading, error, refetch } = useQuery({
    queryKey: [...queryKey, state.pagination.page, state.pagination.pageSize, state.filters, state.sort, state.search],
    queryFn: () => config.handlers.onFetch({
      page: state.pagination.page,
      pageSize: state.pagination.pageSize,
      filters: state.filters,
      sort: state.sort,
      search: state.search,
    }),
    enabled: config.features.read !== false,
  });

  // Update state when query data changes
  useEffect(() => {
    if (queryData) {
      dispatch({
        type: "SET_DATA",
        payload: { data: queryData.data, total: queryData.total },
      });
    }
  }, [queryData]);

  useEffect(() => {
    dispatch({ type: "SET_LOADING", payload: isLoading });
  }, [isLoading]);

  useEffect(() => {
    dispatch({ type: "SET_ERROR", payload: error?.message || null });
  }, [error]);

  // Mutations
  const createMutation = useMutation({
    mutationFn: config.handlers.onCreate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey });
      dispatch({ type: "SET_FORM_MODAL", payload: { open: false, mode: "create" } });
      toast.success(`${config.entityName} created successfully`);
    },
    onError: (error: any) => {
      toast.error(`Failed to create ${config.entityName.toLowerCase()}: ${error.message}`);
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<T> }) => 
      config.handlers.onUpdate(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey });
      dispatch({ type: "SET_FORM_MODAL", payload: { open: false, mode: "edit" } });
      toast.success(`${config.entityName} updated successfully`);
    },
    onError: (error: any) => {
      toast.error(`Failed to update ${config.entityName.toLowerCase()}: ${error.message}`);
    },
  });

  const deleteMutation = useMutation({
    mutationFn: config.handlers.onDelete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey });
      toast.success(`${config.entityName} deleted successfully`);
    },
    onError: (error: any) => {
      toast.error(`Failed to delete ${config.entityName.toLowerCase()}: ${error.message}`);
    },
  });

  // Event handlers
  const handleCreate = () => {
    dispatch({
      type: "SET_FORM_MODAL",
      payload: { open: true, mode: "create" },
    });
  };

  const handleEdit = (record: T) => {
    dispatch({
      type: "SET_FORM_MODAL",
      payload: { open: true, mode: "edit", record },
    });
  };

  const handleDelete = async (record: T) => {
    if (window.confirm(`Are you sure you want to delete this ${config.entityName.toLowerCase()}?`)) {
      deleteMutation.mutate(record.id);
    }
  };

  const handleFormSubmit = async (data: any) => {
    if (state.formMode === "create") {
      await createMutation.mutateAsync(data);
    } else if (state.editingRecord) {
      await updateMutation.mutateAsync({ id: state.editingRecord.id, data });
    }
  };

  const handleSelectionChange = (keys: string[], rows: T[]) => {
    dispatch({
      type: "SET_SELECTED_ROWS",
      payload: { keys, rows },
    });
  };

  const handleFiltersChange = (filters: any) => {
    dispatch({ type: "SET_FILTERS", payload: filters });
    dispatch({ type: "SET_PAGINATION", payload: { page: 1 } }); // Reset to first page
  };

  const handleSearchChange = (search: string) => {
    dispatch({ type: "SET_SEARCH", payload: search });
    dispatch({ type: "SET_PAGINATION", payload: { page: 1 } }); // Reset to first page
  };

  const handleSortChange = (sort: any) => {
    dispatch({ type: "SET_SORT", payload: sort });
  };

  const handlePaginationChange = (page: number, pageSize: number) => {
    dispatch({ type: "SET_PAGINATION", payload: { page, pageSize } });
  };

  const handleExport = async (params: any) => {
    dispatch({ type: "SET_EXPORT_LOADING", payload: true });
    try {
      if (config.handlers.onExport) {
        await config.handlers.onExport(params);
      } else {
        // Default export implementation
        const { data } = await config.handlers.onFetch({
          page: 1,
          pageSize: 10000, // Get all data for export
          filters: params.filters,
          sort: params.sort,
          search: params.search,
        });

        const exportData = params.selectedIds 
          ? data.filter(item => params.selectedIds.includes(item.id))
          : data;

        const csvContent = exportUtils.toCSV(exportData, config.columns, params.config);
        const filename = exportUtils.generateFilename(
          params.config.filename || config.entityNamePlural.toLowerCase(),
          params.format
        );

        exportUtils.downloadFile(csvContent, filename, 'text/csv');
        toast.success("Data exported successfully");
      }
    } catch (error: any) {
      toast.error(`Export failed: ${error.message}`);
    } finally {
      dispatch({ type: "SET_EXPORT_LOADING", payload: false });
    }
  };

  // Bulk operations
  const bulkOperations = useMemo(() => {
    if (config.bulkOperations) {
      return config.bulkOperations;
    }
    return createDefaultBulkOperations<T>(
      config.handlers.onBulkDelete,
      config.handlers.onBulkUpdate
    );
  }, [config.bulkOperations, config.handlers.onBulkDelete, config.handlers.onBulkUpdate]);

  const formInitialData = useMemo(() => {
    if (state.formMode === "edit" && state.editingRecord) {
      return state.editingRecord;
    }
    return config.formFields.reduce((acc, field) => {
      if (field.defaultValue !== undefined) {
        acc[field.key as keyof T] = field.defaultValue;
      }
      return acc;
    }, {} as Partial<T>);
  }, [state.formMode, state.editingRecord, config.formFields]);

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">{config.entityNamePlural}</h2>
              {config.customComponents?.tableHeader}
            </div>
            <div className="flex items-center gap-2">
              {config.features.export && (
                <ExportButton
                  data={state.data}
                  columns={config.columns}
                  selectedRowKeys={state.selectedRowKeys}
                  filters={state.filters}
                  sort={state.sort}
                  search={state.search}
                  onExport={handleExport}
                  loading={state.exportLoading}
                  entityName={config.entityName}
                />
              )}
              
              {config.ui.showRefreshButton && (
                <Button variant="outline" size="sm" onClick={() => refetch()}>
                  <Icon icon="mdi:refresh" className="mr-2 h-4 w-4" />
                  Refresh
                </Button>
              )}

              {config.features.create && (
                <Button onClick={handleCreate}>
                  <Icon icon="mdi:plus" className="mr-2 h-4 w-4" />
                  Create {config.entityName}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Filters */}
      {(config.features.filter || config.features.search) && (
        <FilterPanel
          columns={config.columns}
          filters={state.filters}
          onFiltersChange={handleFiltersChange}
          onClearFilters={() => dispatch({ type: "SET_FILTERS", payload: {} })}
          search={state.search}
          onSearchChange={handleSearchChange}
        />
      )}

      {/* Bulk Operations */}
      {bulkOperations.length > 0 && (
        <BulkOperations
          selectedRowKeys={state.selectedRowKeys}
          selectedRows={state.selectedRows}
          operations={bulkOperations}
          onClearSelection={() => handleSelectionChange([], [])}
          loading={state.bulkOperationLoading}
        />
      )}

      {/* Data Table */}
      <Card>
        <CardContent className="p-0">
          <DataTable
            data={state.data}
            columns={config.columns}
            loading={state.loading}
            selectedRowKeys={state.selectedRowKeys}
            onSelectionChange={handleSelectionChange}
            onSort={handleSortChange}
            onEdit={config.features.update ? handleEdit : undefined}
            onDelete={config.features.delete ? handleDelete : undefined}
            currentSort={state.sort}
            rowKey={config.rowKey}
            size={config.ui.tableSize}
            showRowNumbers={config.ui.showRowNumbers}
            emptyState={config.customComponents?.emptyState}
          />
        </CardContent>
      </Card>

      {/* Form Modal */}
      {(config.features.create || config.features.update) && (
        <FormModal
          open={state.formModalOpen}
          mode={state.formMode}
          title={state.formMode === "create" 
            ? `Create ${config.entityName}` 
            : `Edit ${config.entityName}`
          }
          fields={config.formFields}
          schema={config.formSchema}
          initialData={formInitialData}
          loading={createMutation.isPending || updateMutation.isPending}
          onSubmit={handleFormSubmit}
          onCancel={() => dispatch({ 
            type: "SET_FORM_MODAL", 
            payload: { open: false, mode: "create" } 
          })}
          size={config.ui.formModalSize}
          layout={config.ui.formLayout}
          customHeader={config.customComponents?.formHeader}
          customFooter={config.customComponents?.formFooter}
        />
      )}
    </div>
  );
}
