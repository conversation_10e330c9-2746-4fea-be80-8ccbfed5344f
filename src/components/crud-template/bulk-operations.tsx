import { useState } from "react";
import { But<PERSON> } from "@/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/ui/dropdown-menu";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter, DialogDescription } from "@/ui/dialog";
import { Badge } from "@/ui/badge";
import { Icon } from "@/components/icon";
import { cn } from "@/utils";
import type { BaseEntity, BulkOperation } from "./types";

interface BulkOperationsProps<T extends BaseEntity> {
  selectedRowKeys: string[];
  selectedRows: T[];
  operations: BulkOperation<T>[];
  onClearSelection: () => void;
  loading?: boolean;
  className?: string;
}

export function BulkOperations<T extends BaseEntity>({
  selectedRowKeys,
  selectedRows,
  operations,
  onClearSelection,
  loading = false,
  className,
}: BulkOperationsProps<T>) {
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    operation?: BulkOperation<T>;
    loading: boolean;
  }>({
    open: false,
    loading: false,
  });

  const handleOperation = async (operation: BulkOperation<T>) => {
    if (operation.requiresConfirmation) {
      setConfirmDialog({
        open: true,
        operation,
        loading: false,
      });
    } else {
      await executeOperation(operation);
    }
  };

  const executeOperation = async (operation: BulkOperation<T>) => {
    try {
      setConfirmDialog(prev => ({ ...prev, loading: true }));
      await operation.action(selectedRowKeys, selectedRows);
      setConfirmDialog({ open: false, loading: false });
      onClearSelection();
    } catch (error) {
      console.error("Bulk operation failed:", error);
      setConfirmDialog(prev => ({ ...prev, loading: false }));
    }
  };

  const confirmOperation = async () => {
    if (confirmDialog.operation) {
      await executeOperation(confirmDialog.operation);
    }
  };

  if (selectedRowKeys.length === 0) {
    return null;
  }

  return (
    <>
      <div className={cn("flex items-center gap-2 p-3 bg-muted/50 rounded-lg border", className)}>
        <Badge variant="secondary" className="font-medium">
          {selectedRowKeys.length} selected
        </Badge>

        <div className="flex items-center gap-2">
          {operations.map(operation => (
            <Button
              key={operation.key}
              variant={operation.variant || "outline"}
              size="sm"
              onClick={() => handleOperation(operation)}
              disabled={loading}
              className="gap-2"
            >
              {operation.icon && <Icon icon={operation.icon} className="h-4 w-4" />}
              {operation.label}
            </Button>
          ))}

          <Button
            variant="ghost"
            size="sm"
            onClick={onClearSelection}
            disabled={loading}
            className="gap-2"
          >
            <Icon icon="mdi:close" className="h-4 w-4" />
            Clear
          </Button>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <Dialog 
        open={confirmDialog.open} 
        onOpenChange={(open) => !open && setConfirmDialog({ open: false, loading: false })}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Bulk Operation</DialogTitle>
            <DialogDescription>
              {confirmDialog.operation?.confirmMessage || 
                `Are you sure you want to perform this operation on ${selectedRowKeys.length} selected items?`
              }
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="space-y-2">
              <p className="text-sm font-medium">Selected items:</p>
              <div className="max-h-32 overflow-y-auto space-y-1">
                {selectedRows.slice(0, 10).map((row, index) => (
                  <div key={row.id} className="text-sm text-muted-foreground">
                    {index + 1}. {row.id}
                  </div>
                ))}
                {selectedRows.length > 10 && (
                  <div className="text-sm text-muted-foreground">
                    ... and {selectedRows.length - 10} more items
                  </div>
                )}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setConfirmDialog({ open: false, loading: false })}
              disabled={confirmDialog.loading}
            >
              Cancel
            </Button>
            <Button
              variant={confirmDialog.operation?.variant === "destructive" ? "destructive" : "default"}
              onClick={confirmOperation}
              disabled={confirmDialog.loading}
            >
              {confirmDialog.loading && (
                <Icon icon="mdi:loading" className="mr-2 h-4 w-4 animate-spin" />
              )}
              Confirm
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

// Default bulk operations
export const createDefaultBulkOperations = <T extends BaseEntity>(
  onBulkDelete?: (ids: string[]) => Promise<void>,
  onBulkUpdate?: (ids: string[], data: Partial<T>) => Promise<void>
): BulkOperation<T>[] => {
  const operations: BulkOperation<T>[] = [];

  if (onBulkDelete) {
    operations.push({
      key: "delete",
      label: "Delete Selected",
      icon: "mdi:delete",
      variant: "destructive",
      action: onBulkDelete,
      requiresConfirmation: true,
      confirmMessage: "Are you sure you want to delete the selected items? This action cannot be undone.",
    });
  }

  if (onBulkUpdate) {
    operations.push({
      key: "update",
      label: "Update Selected",
      icon: "mdi:pencil",
      variant: "outline",
      action: async (ids: string[]) => {
        // This would typically open a bulk update form
        // For now, we'll just call the handler with empty data
        await onBulkUpdate(ids, {});
      },
      requiresConfirmation: true,
      confirmMessage: "Are you sure you want to update the selected items?",
    });
  }

  return operations;
};

// Bulk update form component
interface BulkUpdateFormProps<T extends BaseEntity> {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: Partial<T>) => Promise<void>;
  selectedCount: number;
  fields: Array<{
    key: keyof T;
    label: string;
    type: "text" | "select" | "number" | "boolean";
    options?: Array<{ label: string; value: any }>;
  }>;
}

export function BulkUpdateForm<T extends BaseEntity>({
  open,
  onClose,
  onSubmit,
  selectedCount,
  fields,
}: BulkUpdateFormProps<T>) {
  const [formData, setFormData] = useState<Partial<T>>({});
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      await onSubmit(formData);
      setFormData({});
      onClose();
    } catch (error) {
      console.error("Bulk update failed:", error);
    } finally {
      setLoading(false);
    }
  };

  const updateField = (key: keyof T, value: any) => {
    setFormData(prev => ({ ...prev, [key]: value }));
  };

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Bulk Update</DialogTitle>
          <DialogDescription>
            Update {selectedCount} selected items. Only filled fields will be updated.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {fields.map(field => (
            <div key={String(field.key)} className="space-y-2">
              <label className="text-sm font-medium">{field.label}</label>
              {field.type === "text" && (
                <input
                  type="text"
                  className="w-full px-3 py-2 border rounded-md"
                  value={formData[field.key] as string || ""}
                  onChange={(e) => updateField(field.key, e.target.value)}
                  placeholder={`Update ${field.label.toLowerCase()}`}
                />
              )}
              {field.type === "select" && (
                <select
                  className="w-full px-3 py-2 border rounded-md"
                  value={formData[field.key] as string || ""}
                  onChange={(e) => updateField(field.key, e.target.value)}
                >
                  <option value="">No change</option>
                  {field.options?.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              )}
              {field.type === "number" && (
                <input
                  type="number"
                  className="w-full px-3 py-2 border rounded-md"
                  value={formData[field.key] as number || ""}
                  onChange={(e) => updateField(field.key, Number(e.target.value))}
                  placeholder={`Update ${field.label.toLowerCase()}`}
                />
              )}
              {field.type === "boolean" && (
                <select
                  className="w-full px-3 py-2 border rounded-md"
                  value={formData[field.key]?.toString() || ""}
                  onChange={(e) => updateField(field.key, e.target.value === "true")}
                >
                  <option value="">No change</option>
                  <option value="true">Yes</option>
                  <option value="false">No</option>
                </select>
              )}
            </div>
          ))}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={loading}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Icon icon="mdi:loading" className="mr-2 h-4 w-4 animate-spin" />}
              Update {selectedCount} Items
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
