import { Button } from "@/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/ui/dropdown-menu";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/ui/dialog";
import { Checkbox } from "@/ui/checkbox";
import { Input } from "@/ui/input";
import { Label } from "@/ui/label";
import { Icon } from "@/components/icon";
import { useState } from "react";
import type { BaseEntity, CrudColumn, CrudFilters, SortConfig, ExportConfig } from "./types";

interface ExportButtonProps<T extends BaseEntity> {
  data: T[];
  columns: CrudColumn<T>[];
  selectedRowKeys: string[];
  filters: CrudFilters;
  sort?: SortConfig;
  search: string;
  onExport: (params: {
    filters: CrudFilters;
    sort?: SortConfig;
    search: string;
    selectedIds?: string[];
    format: "csv" | "excel";
    config: ExportConfig;
  }) => Promise<void>;
  loading?: boolean;
  entityName: string;
  className?: string;
}

export function ExportButton<T extends BaseEntity>({
  data,
  columns,
  selectedRowKeys,
  filters,
  sort,
  search,
  onExport,
  loading = false,
  entityName,
  className,
}: ExportButtonProps<T>) {
  const [showDialog, setShowDialog] = useState(false);
  const [exportConfig, setExportConfig] = useState<ExportConfig>({
    filename: `${entityName.toLowerCase()}-export`,
    formats: ["csv", "excel"],
    includeHeaders: true,
    selectedOnly: false,
    customFields: columns.map(col => ({
      key: String(col.key),
      label: col.title,
    })),
  });

  const handleQuickExport = async (format: "csv" | "excel") => {
    const timestamp = new Date().toISOString().split('T')[0];
    const config: ExportConfig = {
      ...exportConfig,
      filename: `${entityName.toLowerCase()}-${timestamp}`,
    };

    await onExport({
      filters,
      sort,
      search,
      selectedIds: selectedRowKeys.length > 0 ? selectedRowKeys : undefined,
      format,
      config,
    });
  };

  const handleCustomExport = async (format: "csv" | "excel") => {
    const timestamp = new Date().toISOString().split('T')[0];
    const config: ExportConfig = {
      ...exportConfig,
      filename: exportConfig.filename || `${entityName.toLowerCase()}-${timestamp}`,
    };

    await onExport({
      filters,
      sort,
      search,
      selectedIds: exportConfig.selectedOnly ? selectedRowKeys : undefined,
      format,
      config,
    });

    setShowDialog(false);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" disabled={loading} className={className}>
            {loading ? (
              <Icon icon="mdi:loading" className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Icon icon="mdi:download" className="mr-2 h-4 w-4" />
            )}
            Export
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => handleQuickExport("csv")}>
            <Icon icon="mdi:file-delimited" className="mr-2 h-4 w-4" />
            Export as CSV
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleQuickExport("excel")}>
            <Icon icon="mdi:file-excel" className="mr-2 h-4 w-4" />
            Export as Excel
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setShowDialog(true)}>
            <Icon icon="mdi:cog" className="mr-2 h-4 w-4" />
            Custom Export...
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Custom Export Configuration</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Filename */}
            <div className="space-y-2">
              <Label htmlFor="filename">Filename</Label>
              <Input
                id="filename"
                value={exportConfig.filename}
                onChange={(e) => setExportConfig(prev => ({ ...prev, filename: e.target.value }))}
                placeholder="Enter filename (without extension)"
              />
            </div>

            {/* Export Options */}
            <div className="space-y-4">
              <h4 className="font-medium">Export Options</h4>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeHeaders"
                  checked={exportConfig.includeHeaders}
                  onCheckedChange={(checked) => 
                    setExportConfig(prev => ({ ...prev, includeHeaders: !!checked }))
                  }
                />
                <Label htmlFor="includeHeaders">Include column headers</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="selectedOnly"
                  checked={exportConfig.selectedOnly}
                  onCheckedChange={(checked) => 
                    setExportConfig(prev => ({ ...prev, selectedOnly: !!checked }))
                  }
                  disabled={selectedRowKeys.length === 0}
                />
                <Label htmlFor="selectedOnly">
                  Export selected rows only ({selectedRowKeys.length} selected)
                </Label>
              </div>
            </div>

            {/* Column Selection */}
            <div className="space-y-4">
              <h4 className="font-medium">Select Columns to Export</h4>
              <div className="grid grid-cols-2 gap-2 max-h-60 overflow-y-auto">
                {columns.map(column => {
                  const isSelected = exportConfig.customFields?.some(
                    field => field.key === String(column.key)
                  );
                  
                  return (
                    <div key={String(column.key)} className="flex items-center space-x-2">
                      <Checkbox
                        id={String(column.key)}
                        checked={isSelected}
                        onCheckedChange={(checked) => {
                          setExportConfig(prev => {
                            const customFields = prev.customFields || [];
                            if (checked) {
                              return {
                                ...prev,
                                customFields: [
                                  ...customFields,
                                  { key: String(column.key), label: column.title }
                                ]
                              };
                            } else {
                              return {
                                ...prev,
                                customFields: customFields.filter(
                                  field => field.key !== String(column.key)
                                )
                              };
                            }
                          });
                        }}
                      />
                      <Label htmlFor={String(column.key)} className="text-sm">
                        {column.title}
                      </Label>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => handleCustomExport("csv")}>
              <Icon icon="mdi:file-delimited" className="mr-2 h-4 w-4" />
              Export CSV
            </Button>
            <Button onClick={() => handleCustomExport("excel")}>
              <Icon icon="mdi:file-excel" className="mr-2 h-4 w-4" />
              Export Excel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

// Utility functions for data export
export const exportUtils = {
  // Convert data to CSV format
  toCSV: <T extends BaseEntity>(
    data: T[],
    columns: CrudColumn<T>[],
    config: ExportConfig
  ): string => {
    const selectedFields = config.customFields || columns.map(col => ({
      key: String(col.key),
      label: col.title,
    }));

    const headers = selectedFields.map(field => field.label);
    const rows = data.map(record => 
      selectedFields.map(field => {
        const value = record[field.key as keyof T];
        // Handle special characters and quotes in CSV
        if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value?.toString() || '';
      })
    );

    const csvContent = [
      ...(config.includeHeaders ? [headers] : []),
      ...rows
    ].map(row => row.join(',')).join('\n');

    return csvContent;
  },

  // Download file
  downloadFile: (content: string, filename: string, type: string) => {
    const blob = new Blob([content], { type });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  },

  // Generate filename with timestamp
  generateFilename: (baseName: string, format: string): string => {
    const timestamp = new Date().toISOString().split('T')[0];
    return `${baseName}-${timestamp}.${format}`;
  },
};
