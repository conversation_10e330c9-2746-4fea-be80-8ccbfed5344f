import { z } from "zod";
import { Badge } from "@/ui/badge";
import { Icon } from "@/components/icon";
import { CrudTemplate } from "../crud-template";
import type { BaseEntity, CrudConfig, CrudColumn, CrudFormField } from "../types";

// Product entity interface
interface Product extends BaseEntity {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  status: "active" | "inactive" | "discontinued";
  stock: number;
  sku: string;
  createdAt: string;
  updatedAt: string;
}

// Product validation schema
const productSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  description: z.string().min(1, "Description is required").max(500, "Description must be less than 500 characters"),
  price: z.number().min(0, "Price must be positive"),
  category: z.string().min(1, "Category is required"),
  status: z.enum(["active", "inactive", "discontinued"]),
  stock: z.number().int().min(0, "Stock must be a positive integer"),
  sku: z.string().min(1, "SKU is required").max(50, "SKU must be less than 50 characters"),
});

// Column definitions
const productColumns: CrudColumn<Product>[] = [
  {
    key: "sku",
    title: "SKU",
    dataIndex: "sku",
    width: 120,
    sortable: true,
    filterable: true,
    searchable: true,
    filterType: "text",
  },
  {
    key: "name",
    title: "Product Name",
    dataIndex: "name",
    width: 200,
    sortable: true,
    filterable: true,
    searchable: true,
    filterType: "text",
    render: (value, record) => (
      <div className="flex items-center gap-2">
        <div className="w-8 h-8 bg-muted rounded flex items-center justify-center">
          <Icon icon="mdi:package-variant" size={16} />
        </div>
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-xs text-muted-foreground">{record.sku}</div>
        </div>
      </div>
    ),
  },
  {
    key: "category",
    title: "Category",
    dataIndex: "category",
    width: 120,
    sortable: true,
    filterable: true,
    filterType: "select",
    filterOptions: [
      { label: "Electronics", value: "electronics" },
      { label: "Clothing", value: "clothing" },
      { label: "Books", value: "books" },
      { label: "Home & Garden", value: "home-garden" },
      { label: "Sports", value: "sports" },
    ],
  },
  {
    key: "price",
    title: "Price",
    dataIndex: "price",
    width: 100,
    align: "right",
    sortable: true,
    filterable: true,
    filterType: "number",
    render: (value) => `$${value.toFixed(2)}`,
  },
  {
    key: "stock",
    title: "Stock",
    dataIndex: "stock",
    width: 80,
    align: "center",
    sortable: true,
    filterable: true,
    filterType: "number",
    render: (value) => (
      <Badge variant={value > 10 ? "default" : value > 0 ? "secondary" : "destructive"}>
        {value}
      </Badge>
    ),
  },
  {
    key: "status",
    title: "Status",
    dataIndex: "status",
    width: 120,
    sortable: true,
    filterable: true,
    filterType: "select",
    filterOptions: [
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
      { label: "Discontinued", value: "discontinued" },
    ],
    render: (value) => {
      const variants = {
        active: "default",
        inactive: "secondary",
        discontinued: "destructive",
      } as const;
      return <Badge variant={variants[value as keyof typeof variants]}>{value}</Badge>;
    },
  },
  {
    key: "createdAt",
    title: "Created",
    dataIndex: "createdAt",
    width: 120,
    sortable: true,
    filterable: true,
    filterType: "date",
    render: (value) => new Date(value).toLocaleDateString(),
    responsive: "md",
  },
];

// Form field definitions
const productFormFields: CrudFormField<Product>[] = [
  {
    key: "name",
    label: "Product Name",
    type: "text",
    placeholder: "Enter product name",
    required: true,
    gridCols: 2,
  },
  {
    key: "sku",
    label: "SKU",
    type: "text",
    placeholder: "Enter SKU",
    required: true,
    gridCols: 1,
  },
  {
    key: "category",
    label: "Category",
    type: "select",
    placeholder: "Select category",
    required: true,
    options: [
      { label: "Electronics", value: "electronics" },
      { label: "Clothing", value: "clothing" },
      { label: "Books", value: "books" },
      { label: "Home & Garden", value: "home-garden" },
      { label: "Sports", value: "sports" },
    ],
    gridCols: 1,
  },
  {
    key: "description",
    label: "Description",
    type: "textarea",
    placeholder: "Enter product description",
    required: true,
    gridCols: 2,
  },
  {
    key: "price",
    label: "Price",
    type: "number",
    placeholder: "0.00",
    required: true,
    gridCols: 1,
  },
  {
    key: "stock",
    label: "Stock Quantity",
    type: "number",
    placeholder: "0",
    required: true,
    gridCols: 1,
  },
  {
    key: "status",
    label: "Status",
    type: "select",
    placeholder: "Select status",
    required: true,
    options: [
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
      { label: "Discontinued", value: "discontinued" },
    ],
    gridCols: 1,
    defaultValue: "active",
  },
];

// Mock API functions (replace with real API calls)
const mockProducts: Product[] = [
  {
    id: "1",
    name: "Wireless Headphones",
    description: "High-quality wireless headphones with noise cancellation",
    price: 199.99,
    category: "electronics",
    status: "active",
    stock: 25,
    sku: "WH-001",
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-15T10:00:00Z",
  },
  {
    id: "2",
    name: "Cotton T-Shirt",
    description: "Comfortable cotton t-shirt in various colors",
    price: 29.99,
    category: "clothing",
    status: "active",
    stock: 50,
    sku: "TS-002",
    createdAt: "2024-01-16T10:00:00Z",
    updatedAt: "2024-01-16T10:00:00Z",
  },
  // Add more mock products as needed
];

const productHandlers = {
  onFetch: async (params: any) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    let filteredData = [...mockProducts];
    
    // Apply search
    if (params.search) {
      filteredData = filteredData.filter(product =>
        product.name.toLowerCase().includes(params.search.toLowerCase()) ||
        product.sku.toLowerCase().includes(params.search.toLowerCase()) ||
        product.description.toLowerCase().includes(params.search.toLowerCase())
      );
    }
    
    // Apply filters
    Object.entries(params.filters).forEach(([key, filter]: [string, any]) => {
      filteredData = filteredData.filter(product => {
        const value = product[key as keyof Product];
        switch (filter.operator) {
          case "contains":
            return String(value).toLowerCase().includes(String(filter.value).toLowerCase());
          case "eq":
            return value === filter.value;
          case "gt":
            return Number(value) > Number(filter.value);
          case "gte":
            return Number(value) >= Number(filter.value);
          case "lt":
            return Number(value) < Number(filter.value);
          case "lte":
            return Number(value) <= Number(filter.value);
          default:
            return true;
        }
      });
    });
    
    // Apply sorting
    if (params.sort) {
      filteredData.sort((a, b) => {
        const aValue = a[params.sort.field as keyof Product];
        const bValue = b[params.sort.field as keyof Product];
        const direction = params.sort.direction === "asc" ? 1 : -1;
        
        if (aValue < bValue) return -1 * direction;
        if (aValue > bValue) return 1 * direction;
        return 0;
      });
    }
    
    // Apply pagination
    const start = (params.page - 1) * params.pageSize;
    const end = start + params.pageSize;
    const paginatedData = filteredData.slice(start, end);
    
    return {
      data: paginatedData,
      total: filteredData.length,
    };
  },

  onCreate: async (data: Omit<Product, "id" | "createdAt" | "updatedAt">) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    const newProduct: Product = {
      ...data,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    mockProducts.push(newProduct);
    return newProduct;
  },

  onUpdate: async (id: string, data: Partial<Product>) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    const index = mockProducts.findIndex(p => p.id === id);
    if (index === -1) throw new Error("Product not found");
    
    mockProducts[index] = {
      ...mockProducts[index],
      ...data,
      updatedAt: new Date().toISOString(),
    };
    return mockProducts[index];
  },

  onDelete: async (id: string) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    const index = mockProducts.findIndex(p => p.id === id);
    if (index === -1) throw new Error("Product not found");
    mockProducts.splice(index, 1);
  },

  onBulkDelete: async (ids: string[]) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    ids.forEach(id => {
      const index = mockProducts.findIndex(p => p.id === id);
      if (index !== -1) mockProducts.splice(index, 1);
    });
  },
};

// CRUD configuration
const productCrudConfig: CrudConfig<Product> = {
  entityName: "Product",
  entityNamePlural: "Products",
  columns: productColumns,
  formFields: productFormFields,
  formSchema: productSchema,
  rowKey: "id",
  
  features: {
    create: true,
    read: true,
    update: true,
    delete: true,
    bulkDelete: true,
    export: true,
    search: true,
    filter: true,
    sort: true,
    pagination: true,
  },
  
  ui: {
    tableSize: "small",
    formLayout: "vertical",
    formModalSize: "lg",
    showRowNumbers: true,
    showRefreshButton: true,
    showColumnToggle: true,
  },
  
  pagination: {
    pageSize: 10,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: true,
    pageSizeOptions: [10, 20, 50, 100],
  },
  
  exportConfig: {
    formats: ["csv", "excel"],
    includeHeaders: true,
  },
  
  handlers: productHandlers,
};

// Product CRUD Component
export function ProductCrud() {
  return (
    <div className="container mx-auto py-6">
      <CrudTemplate
        config={productCrudConfig}
        queryKey={["products"]}
      />
    </div>
  );
}
