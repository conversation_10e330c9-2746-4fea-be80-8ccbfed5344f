import { useState } from "react";
import { But<PERSON> } from "@/ui/button";
import { Input } from "@/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/ui/select";
import { Calendar } from "@/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/popover";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { Separator } from "@/ui/separator";
import { Icon } from "@/components/icon";
import { cn } from "@/utils";
import { format } from "date-fns";
import type { BaseEntity, CrudColumn, CrudFilters, FilterValue } from "./types";

interface FilterPanelProps<T extends BaseEntity> {
  columns: CrudColumn<T>[];
  filters: CrudFilters;
  onFiltersChange: (filters: CrudFilters) => void;
  onClearFilters: () => void;
  search: string;
  onSearchChange: (search: string) => void;
  className?: string;
}

export function FilterPanel<T extends BaseEntity>({
  columns,
  filters,
  onFiltersChange,
  onClearFilters,
  search,
  onSearchChange,
  className,
}: FilterPanelProps<T>) {
  const [showAdvanced, setShowAdvanced] = useState(false);

  const filterableColumns = columns.filter(col => col.filterable);
  const searchableColumns = columns.filter(col => col.searchable);
  const activeFiltersCount = Object.keys(filters).length;

  const updateFilter = (key: string, filterValue: FilterValue | null) => {
    const newFilters = { ...filters };
    if (filterValue === null) {
      delete newFilters[key];
    } else {
      newFilters[key] = filterValue;
    }
    onFiltersChange(newFilters);
  };

  const renderFilterInput = (column: CrudColumn<T>) => {
    const filterKey = String(column.key);
    const currentFilter = filters[filterKey];

    switch (column.filterType) {
      case "text":
        return (
          <Input
            placeholder={`Filter by ${column.title.toLowerCase()}`}
            value={currentFilter?.value || ""}
            onChange={(e) => {
              const value = e.target.value;
              updateFilter(filterKey, value ? {
                type: "text",
                value,
                operator: "contains"
              } : null);
            }}
          />
        );

      case "select":
        return (
          <Select
            value={currentFilter?.value || ""}
            onValueChange={(value) => {
              updateFilter(filterKey, value ? {
                type: "select",
                value,
                operator: "eq"
              } : null);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder={`Filter by ${column.title.toLowerCase()}`} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All</SelectItem>
              {column.filterOptions?.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case "date":
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !currentFilter?.value && "text-muted-foreground"
                )}
              >
                <Icon icon="mdi:calendar" className="mr-2 h-4 w-4" />
                {currentFilter?.value ? (
                  format(new Date(currentFilter.value), "PPP")
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={currentFilter?.value ? new Date(currentFilter.value) : undefined}
                onSelect={(date) => {
                  updateFilter(filterKey, date ? {
                    type: "date",
                    value: date.toISOString(),
                    operator: "eq"
                  } : null);
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        );

      case "dateRange":
        return (
          <div className="space-y-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !currentFilter?.value?.from && "text-muted-foreground"
                  )}
                >
                  <Icon icon="mdi:calendar" className="mr-2 h-4 w-4" />
                  {currentFilter?.value?.from ? (
                    format(new Date(currentFilter.value.from), "PPP")
                  ) : (
                    <span>From date</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={currentFilter?.value?.from ? new Date(currentFilter.value.from) : undefined}
                  onSelect={(date) => {
                    const currentValue = currentFilter?.value || {};
                    updateFilter(filterKey, {
                      type: "dateRange",
                      value: { ...currentValue, from: date?.toISOString() },
                      operator: "between"
                    });
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !currentFilter?.value?.to && "text-muted-foreground"
                  )}
                >
                  <Icon icon="mdi:calendar" className="mr-2 h-4 w-4" />
                  {currentFilter?.value?.to ? (
                    format(new Date(currentFilter.value.to), "PPP")
                  ) : (
                    <span>To date</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={currentFilter?.value?.to ? new Date(currentFilter.value.to) : undefined}
                  onSelect={(date) => {
                    const currentValue = currentFilter?.value || {};
                    updateFilter(filterKey, {
                      type: "dateRange",
                      value: { ...currentValue, to: date?.toISOString() },
                      operator: "between"
                    });
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        );

      case "number":
        return (
          <div className="flex gap-2">
            <Select
              value={currentFilter?.operator || "eq"}
              onValueChange={(operator) => {
                if (currentFilter) {
                  updateFilter(filterKey, {
                    ...currentFilter,
                    operator: operator as any
                  });
                }
              }}
            >
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="eq">=</SelectItem>
                <SelectItem value="ne">≠</SelectItem>
                <SelectItem value="gt">></SelectItem>
                <SelectItem value="gte">≥</SelectItem>
                <SelectItem value="lt"><</SelectItem>
                <SelectItem value="lte">≤</SelectItem>
              </SelectContent>
            </Select>
            <Input
              type="number"
              placeholder="Value"
              value={currentFilter?.value || ""}
              onChange={(e) => {
                const value = e.target.value;
                updateFilter(filterKey, value ? {
                  type: "number",
                  value: Number(value),
                  operator: currentFilter?.operator || "eq"
                } : null);
              }}
            />
          </div>
        );

      case "boolean":
        return (
          <Select
            value={currentFilter?.value?.toString() || ""}
            onValueChange={(value) => {
              updateFilter(filterKey, value ? {
                type: "boolean",
                value: value === "true",
                operator: "eq"
              } : null);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select value" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All</SelectItem>
              <SelectItem value="true">Yes</SelectItem>
              <SelectItem value="false">No</SelectItem>
            </SelectContent>
          </Select>
        );

      default:
        return (
          <Input
            placeholder={`Filter by ${column.title.toLowerCase()}`}
            value={currentFilter?.value || ""}
            onChange={(e) => {
              const value = e.target.value;
              updateFilter(filterKey, value ? {
                type: "text",
                value,
                operator: "contains"
              } : null);
            }}
          />
        );
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Global Search */}
      {searchableColumns.length > 0 && (
        <div className="relative">
          <Icon icon="mdi:magnify" className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder={`Search in ${searchableColumns.map(col => col.title.toLowerCase()).join(", ")}...`}
            value={search}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
      )}

      {/* Quick Filters */}
      <div className="flex items-center gap-2 flex-wrap">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowAdvanced(!showAdvanced)}
        >
          <Icon icon="mdi:filter" className="mr-2 h-4 w-4" />
          Advanced Filters
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-2">
              {activeFiltersCount}
            </Badge>
          )}
        </Button>

        {activeFiltersCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
          >
            <Icon icon="mdi:close" className="mr-2 h-4 w-4" />
            Clear Filters
          </Button>
        )}
      </div>

      {/* Advanced Filter Panel */}
      {showAdvanced && filterableColumns.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Advanced Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filterableColumns.map(column => (
                <div key={String(column.key)} className="space-y-2">
                  <label className="text-sm font-medium">
                    {column.title}
                  </label>
                  {renderFilterInput(column)}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          {Object.entries(filters).map(([key, filter]) => {
            const column = columns.find(col => String(col.key) === key);
            return (
              <Badge key={key} variant="secondary" className="gap-1">
                {column?.title}: {String(filter.value)}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1"
                  onClick={() => updateFilter(key, null)}
                >
                  <Icon icon="mdi:close" className="h-3 w-3" />
                </Button>
              </Badge>
            );
          })}
        </div>
      )}
    </div>
  );
}
