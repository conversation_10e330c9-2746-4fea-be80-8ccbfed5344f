# CRUD Template - Quick Usage Guide

## Installation

The CRUD template is already included in the project. Simply import and use:

```typescript
import { CrudTemplate } from "@/components/crud-template";
```

## Basic Setup (5 Steps)

### Step 1: Define Your Entity Type

```typescript
import type { BaseEntity } from "@/components/crud-template";

interface User extends BaseEntity {
  id: string;
  name: string;
  email: string;
  role: "admin" | "user";
  status: "active" | "inactive";
  createdAt: string;
  updatedAt: string;
}
```

### Step 2: Create Validation Schema

```typescript
import { z } from "zod";

const userSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email"),
  role: z.enum(["admin", "user"]),
  status: z.enum(["active", "inactive"]),
});
```

### Step 3: Define Table Columns

```typescript
import type { CrudColumn } from "@/components/crud-template";

const columns: CrudColumn<User>[] = [
  {
    key: "name",
    title: "Name",
    sortable: true,
    filterable: true,
    searchable: true,
    filterType: "text",
  },
  {
    key: "email",
    title: "Email",
    sortable: true,
    searchable: true,
  },
  {
    key: "role",
    title: "Role",
    filterable: true,
    filterType: "select",
    filterOptions: [
      { label: "Admin", value: "admin" },
      { label: "User", value: "user" },
    ],
  },
  {
    key: "status",
    title: "Status",
    filterable: true,
    filterType: "select",
    filterOptions: [
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
    ],
    render: (value) => (
      <Badge variant={value === "active" ? "default" : "secondary"}>
        {value}
      </Badge>
    ),
  },
];
```

### Step 4: Define Form Fields

```typescript
import type { CrudFormField } from "@/components/crud-template";

const formFields: CrudFormField<User>[] = [
  {
    key: "name",
    label: "Full Name",
    type: "text",
    required: true,
    placeholder: "Enter full name",
  },
  {
    key: "email",
    label: "Email Address",
    type: "email",
    required: true,
    placeholder: "Enter email address",
  },
  {
    key: "role",
    label: "Role",
    type: "select",
    required: true,
    options: [
      { label: "Admin", value: "admin" },
      { label: "User", value: "user" },
    ],
  },
  {
    key: "status",
    label: "Status",
    type: "select",
    required: true,
    options: [
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
    ],
    defaultValue: "active",
  },
];
```

### Step 5: Implement API Handlers

```typescript
import type { CrudHandlers } from "@/components/crud-template";

const handlers: CrudHandlers<User> = {
  onFetch: async (params) => {
    // Convert params to query string
    const queryParams = new URLSearchParams({
      page: params.page.toString(),
      pageSize: params.pageSize.toString(),
      search: params.search || "",
      ...Object.fromEntries(
        Object.entries(params.filters).map(([key, filter]) => [
          `filter_${key}`,
          JSON.stringify(filter)
        ])
      ),
    });

    const response = await fetch(`/api/users?${queryParams}`);
    if (!response.ok) throw new Error("Failed to fetch users");
    
    return response.json(); // Should return { data: User[], total: number }
  },

  onCreate: async (data) => {
    const response = await fetch("/api/users", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
    });
    if (!response.ok) throw new Error("Failed to create user");
    return response.json();
  },

  onUpdate: async (id, data) => {
    const response = await fetch(`/api/users/${id}`, {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
    });
    if (!response.ok) throw new Error("Failed to update user");
    return response.json();
  },

  onDelete: async (id) => {
    const response = await fetch(`/api/users/${id}`, {
      method: "DELETE",
    });
    if (!response.ok) throw new Error("Failed to delete user");
  },

  // Optional: Bulk operations
  onBulkDelete: async (ids) => {
    const response = await fetch("/api/users/bulk-delete", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ ids }),
    });
    if (!response.ok) throw new Error("Failed to delete users");
  },
};
```

### Step 6: Create Configuration and Component

```typescript
import { CrudTemplate } from "@/components/crud-template";
import type { CrudConfig } from "@/components/crud-template";

const config: CrudConfig<User> = {
  entityName: "User",
  entityNamePlural: "Users",
  columns,
  formFields,
  formSchema: userSchema,
  handlers,
  
  features: {
    create: true,
    read: true,
    update: true,
    delete: true,
    bulkDelete: true,
    export: true,
    search: true,
    filter: true,
    sort: true,
    pagination: true,
  },
  
  ui: {
    tableSize: "small",
    formLayout: "vertical",
    formModalSize: "md",
    showRowNumbers: false,
    showRefreshButton: true,
  },
};

export function UserManagement() {
  return (
    <div className="container mx-auto py-6">
      <CrudTemplate
        config={config}
        queryKey={["users"]}
      />
    </div>
  );
}
```

## Common Patterns

### Custom Column Rendering

```typescript
{
  key: "avatar",
  title: "Avatar",
  render: (value, record) => (
    <Avatar>
      <AvatarImage src={record.avatarUrl} />
      <AvatarFallback>{record.name.charAt(0)}</AvatarFallback>
    </Avatar>
  ),
}
```

### Date Formatting

```typescript
{
  key: "createdAt",
  title: "Created",
  render: (value) => format(new Date(value), "MMM dd, yyyy"),
}
```

### Custom Form Field

```typescript
{
  key: "tags",
  label: "Tags",
  type: "custom",
  render: (field, form) => (
    <TagInput
      value={field.value || []}
      onChange={field.onChange}
      placeholder="Add tags..."
    />
  ),
}
```

### Conditional Features

```typescript
const config: CrudConfig<User> = {
  // ... other config
  features: {
    create: userPermissions.canCreate,
    update: userPermissions.canUpdate,
    delete: userPermissions.canDelete,
    // ... other features
  },
};
```

## API Response Format

Your API should return data in this format:

### Fetch Response
```json
{
  "data": [
    {
      "id": "1",
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "admin",
      "status": "active",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 100
}
```

### Create/Update Response
```json
{
  "id": "1",
  "name": "John Doe",
  "email": "<EMAIL>",
  "role": "admin",
  "status": "active",
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

## Error Handling

The template automatically handles errors and shows toast notifications. Make sure your API returns proper HTTP status codes:

- `200`: Success
- `400`: Bad Request (validation errors)
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error

## Next Steps

1. **Customize Styling**: Modify the UI configuration to match your design
2. **Add Validation**: Enhance the Zod schema with custom validation rules
3. **Implement Permissions**: Add role-based access control
4. **Add Tests**: Write unit tests for your CRUD implementation
5. **Optimize Performance**: Add caching and pagination strategies

## Need Help?

- Check the full documentation in `README.md`
- Look at the complete example in `examples/product-crud.tsx`
- Review the type definitions in `types.ts`
