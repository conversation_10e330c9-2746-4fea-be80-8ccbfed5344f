<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48" fill="none">
  <g filter="url(#filter0_iii_3051_46941)">
    <rect x="1" y="1" width="46" height="46" rx="11" stroke="url(#paint2_linear_3051_46941)" stroke-width="2" />
    <g clip-path="url(#clip0_3051_46941)">
      <rect width="48" height="48" rx="11" fill="currentColor" />
      <rect width="48" height="48" fill="url(#paint0_linear_3051_46941)" />
      <g filter="url(#filter1_d_3051_46941)">
        <path fill-rule="evenodd" clip-rule="evenodd"
          d="M15 9C11.6863 9 9 11.6863 9 15V33C9 36.3137 11.6863 39 15 39H33C36.3137 39 39 36.3137 39 33V15C39 11.6863 36.3137 9 33 9H15ZM17.25 20.625H24.7233L15.9242 29.4242L18.5758 32.0758L27.375 23.2767V30.75H31.125V18.75C31.125 17.7145 30.2855 16.875 29.25 16.875H17.25V20.625Z"
          fill="url(#paint1_linear_3051_46941)" />
      </g>
    </g>
  </g>
  <defs>
    <filter id="filter0_iii_3051_46941" x="0" y="-3" width="48" height="54" filterUnits="userSpaceOnUse"
      color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset dy="-3" />
      <feGaussianBlur stdDeviation="1.5" />
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
      <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3051_46941" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset dy="3" />
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0" />
      <feBlend mode="normal" in2="effect1_innerShadow_3051_46941" result="effect2_innerShadow_3051_46941" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <!-- <feMorphology radius="1" operator="erode" in="SourceAlpha" result="effect3_innerShadow_3051_46941" /> -->
      <feOffset />
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0" />
      <feBlend mode="normal" in2="effect2_innerShadow_3051_46941" result="effect3_innerShadow_3051_46941" />
    </filter>
    <filter id="filter1_d_3051_46941" x="6" y="5.25" width="36" height="42" filterUnits="userSpaceOnUse"
      color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feMorphology radius="1.5" operator="erode" in="SourceAlpha" result="effect1_dropShadow_3051_46941" />
      <feOffset dy="2.25" />
      <feGaussianBlur stdDeviation="2.25" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0.141176 0 0 0 0 0.141176 0 0 0 0 0.141176 0 0 0 0.1 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3051_46941" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3051_46941" result="shape" />
    </filter>
    <linearGradient id="paint0_linear_3051_46941" x1="24" y1="5.96047e-07" x2="26" y2="48"
      gradientUnits="userSpaceOnUse">
      <stop stop-color="white" stop-opacity="0" />
      <stop offset="1" stop-color="white" stop-opacity="0.12" />
    </linearGradient>
    <linearGradient id="paint1_linear_3051_46941" x1="24" y1="9" x2="24" y2="39" gradientUnits="userSpaceOnUse">
      <stop stop-color="white" stop-opacity="0.8" />
      <stop offset="1" stop-color="white" stop-opacity="0.5" />
    </linearGradient>
    <linearGradient id="paint2_linear_3051_46941" x1="24" y1="0" x2="24" y2="48" gradientUnits="userSpaceOnUse">
      <stop stop-color="white" stop-opacity="0.12" />
      <stop offset="1" stop-color="white" stop-opacity="0" />
    </linearGradient>
    <clipPath id="clip0_3051_46941">
      <rect width="48" height="48" rx="12" fill="white" />
    </clipPath>
  </defs>
</svg>