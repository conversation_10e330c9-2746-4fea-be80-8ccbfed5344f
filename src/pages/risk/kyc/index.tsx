import type {
	<PERSON>Entity,
	<PERSON>rud<PERSON><PERSON>umn,
	CrudConfig,
	CrudFormField,
	CrudHandlers,
	CrudTemplate,
} from "@/components/crud-template";
import { z } from "zod";

interface Product extends BaseEntity {
	id: string;
	name: string;
	price: number;
	category: string;
	status: "active" | "inactive";
	createdAt: string;
	updatedAt: string;
}

const columns: CrudColumn<Product>[] = [
	{
		key: "name",
		title: "Product Name",
		sortable: true,
		filterable: true,
		searchable: true,
		filterType: "text",
	},
	{
		key: "price",
		title: "Price",
		sortable: true,
		filterable: true,
		filterType: "number",
		render: (value) => `$${value.toFixed(2)}`,
	},
	{
		key: "status",
		title: "Status",
		filterable: true,
		filterType: "select",
		filterOptions: [
			{ label: "Active", value: "active" },
			{ label: "Inactive", value: "inactive" },
		],
	},
];

const formFields: CrudFormField<Product>[] = [
	{
		key: "name",
		label: "Product Name",
		type: "text",
		required: true,
		placeholder: "Enter product name",
	},
	{
		key: "price",
		label: "Price",
		type: "number",
		required: true,
		placeholder: "0.00",
	},
	{
		key: "status",
		label: "Status",
		type: "select",
		required: true,
		options: [
			{ label: "Active", value: "active" },
			{ label: "Inactive", value: "inactive" },
		],
	},
];

const schema = z.object({
	name: z.string().min(1, "Name is required"),
	price: z.number().min(0, "Price must be positive"),
	status: z.enum(["active", "inactive"]),
});

const handlers: CrudHandlers<Product> = {
	onFetch: async (params) => {
		const response = await fetch(`/api/products?${new URLSearchParams(params)}`);
		return response.json();
	},

	onCreate: async (data) => {
		const response = await fetch("/api/products", {
			method: "POST",
			headers: { "Content-Type": "application/json" },
			body: JSON.stringify(data),
		});
		return response.json();
	},

	onUpdate: async (id, data) => {
		const response = await fetch(`/api/products/${id}`, {
			method: "PUT",
			headers: { "Content-Type": "application/json" },
			body: JSON.stringify(data),
		});
		return response.json();
	},

	onDelete: async (id) => {
		await fetch(`/api/products/${id}`, { method: "DELETE" });
	},
};

const config: CrudConfig<Product> = {
	entityName: "Product",
	entityNamePlural: "Products",
	columns,
	formFields,
	formSchema: schema,
	handlers,

	features: {
		create: true,
		read: true,
		update: true,
		delete: true,
		bulkDelete: true,
		export: true,
		search: true,
		filter: true,
		sort: true,
		pagination: true,
	},

	ui: {
		tableSize: "small",
		formLayout: "vertical",
		formModalSize: "lg",
		showRowNumbers: true,
		showRefreshButton: true,
	},
};

export function ProductManagement() {
	return <CrudTemplate config={config} queryKey={["products"]} />;
}
