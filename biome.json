{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true, "defaultBranch": "main"}, "files": {"ignoreUnknown": false, "ignore": ["public", ".vscode", "src/ui"]}, "formatter": {"enabled": true, "lineWidth": 120, "indentStyle": "tab"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "off"}, "a11y": {"useKeyWithClickEvents": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}}