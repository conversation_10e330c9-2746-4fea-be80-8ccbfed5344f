{
  "npm.packageManager": "pnpm",
  "typescript.tsdk": "./node_modules/typescript/lib",

  "editor.tabSize": 2,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "quickfix.biome": "explicit",
    "source.organizeImports.biome": "explicit"
  },

  "tailwindCSS.classFunctions": [ "cn", "clsx", "cva", "twMerge", ],
  "tailwindCSS.emmetCompletions": true,

  "i18n-ally.localesPaths": [ "src/locales/lang" ],
  "i18n-ally.enabledParsers": [ "json" ],
  "i18n-ally.pathMatcher": "{locale}/{namespaces}.{ext}",
  "i18n-ally.keystyle": "flat",
  "i18n-ally.sortKeys": true,
  "i18n-ally.sourceLanguage": "en_US",
  "i18n-ally.displayLanguage": "zh_CN",

  "[javascript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[typescript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[json]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "svg.preview.background": "dark-transparent"
}