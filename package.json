{"name": "slash-admin", "private": true, "version": "0.0.0", "type": "module", "homepage": "https://github.com/d3george/slash-admin", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "preinstall": "lefthook install"}, "dependencies": {"@ant-design/cssinjs": "^1.22.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fontsource-variable/inter": "^5.1.0", "@fontsource-variable/open-sans": "^5.1.0", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@hookform/resolvers": "^5.0.1", "@iconify/react": "^4.1.1", "@iconify/utils": "^2.3.0", "@tailwindcss/vite": "^4.1.3", "@tanstack/react-query": "^5.60.2", "@vanilla-extract/css": "^1.17.0", "@vanilla-extract/vite-plugin": "^5.0.7", "@vercel/analytics": "^1.5.0", "@vitejs/plugin-react": "^4.3.3", "antd": "^5.22.1", "apexcharts": "^4.5.0", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "color": "^4.2.3", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "highlight.js": "^11.10.0", "i18next": "^23.16.5", "i18next-browser-languagedetector": "^7.2.1", "input-otp": "^1.4.2", "lucide-react": "^0.487.0", "motion": "^12.9.0", "numeral": "^2.0.6", "qrcode.react": "^4.2.0", "radix-ui": "^1.4.2", "ramda": "^0.29.1", "react": "^19.1.0", "react-apexcharts": "^1.5.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.1", "react-i18next": "^13.5.0", "react-quill": "^2.0.0", "react-router": "^7.0.2", "react-use": "^17.5.1", "screenfull": "^6.0.2", "sonner": "^1.7.0", "styled-components": "^6.1.13", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "zod": "^3.24.3", "zustand": "^4.5.5"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@faker-js/faker": "^8.4.1", "@types/color": "^3.0.6", "@types/numeral": "^2.0.5", "@types/ramda": "^0.29.12", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/styled-components": "^5.1.34", "lefthook": "^1.8.2", "msw": "^2.6.4", "react-scan": "^0.3.4", "rollup-plugin-visualizer": "^5.12.0", "shiki": "^3.6.0", "tailwindcss": "^4.1.3", "typescript": "^5.6.3", "vite": "^6.2.0", "vite-tsconfig-paths": "^5.1.2"}, "engines": {"node": "20.*"}, "packageManager": "pnpm@10.8.0", "msw": {"workerDirectory": ["public"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}